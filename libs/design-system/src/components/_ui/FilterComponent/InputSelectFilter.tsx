import React, { useState, useMemo, useEffect } from 'react';
import { FilterComponentProps, optionsMap } from './config';
import Icon from '../Icon';
import { useTranslation } from 'react-i18next';
import IconTriangleDown from '../Icon/IconTriangleDown';

const InputSelectFilter: React.FC<FilterComponentProps> = ({
  labelCh,
  labelEn,
  value,
  onChange,
}) => {
  const { i18n } = useTranslation();
  const [showOptions, setShowOptions] = useState(false);
  const [inputValue, setInputValue] = useState('');

  // 使用 useMemo 优化 options 获取
  const options = useMemo(() => {
    return optionsMap[value.value] || [];
  }, [value.value]);

  // 根据输入值过滤选项
  const filteredOptions = useMemo(() => {
    if (!inputValue.trim()) {
      return options;
    }

    return options.filter((option) => {
      const label = i18n.language === 'en' ? option.labelEn : option.labelCh;
      return label.toLowerCase().includes(inputValue.toLowerCase());
    });
  }, [options, inputValue, i18n.language]);

  // 初始化输入值，显示当前选中的选项标签
  useEffect(() => {
    if (value.data && options.length > 0) {
      const selectedOption = options.find(
        (option) => option.value === value.data
      );
      if (selectedOption) {
        const label =
          i18n.language === 'en'
            ? selectedOption.labelEn
            : selectedOption.labelCh;
        setInputValue(label);
      }
    } else if (!value.data) {
      setInputValue('');
    }
  }, [value.data, options, i18n.language]);

  return (
    <div className="flex flex-row items-center gap-2">
      <div className="flex-none inline-flex relative">
        <input
          className="appearance-none peer cursor-pointer w-[21px] h-[21px] border-2 rounded-md border-primary-500 checked:bg-primary-500 disabled:bg-grey-200 disabled:border-grey-200 checked:after:text-white"
          type="checkbox"
          checked={value.checked}
          onChange={() => onChange({ ...value, checked: !value.checked })}
        />
        <Icon
          size={11}
          name="check"
          className={
            'absolute left-[5px] top-[5px] hidden peer-checked:block text-white pointer-events-none'
          }
        />
      </div>

      <label
        className="basis-1/5 pr-6"
        style={{ textAlignLast: 'justify' }}
      >
        {i18n.language === 'en' ? labelEn : labelCh}
      </label>
      <div className="basis-4/5 relative">
        <input
          className="w-full h-full outline-none ring-0 p-2 border border-grey-300 rounded-md disabled:bg-grey-200 disabled:text-neutral-500 focus:border-primary"
          value={inputValue}
          disabled={!value.checked}
          placeholder={
            i18n.language === 'en' ? 'please input select option' : '請輸入選項'
          }
          onFocus={() => {
            setShowOptions(true);
            // 如果当前有选中的值，显示对应的标签
            if (value.data && !inputValue) {
              const selectedOption = options.find(
                (option) => option.value === value.data
              );
              if (selectedOption) {
                setInputValue(
                  i18n.language === 'en'
                    ? selectedOption.labelEn
                    : selectedOption.labelCh
                );
              }
            }
          }}
          onBlur={() => {
            setTimeout(() => setShowOptions(false), 150); // 延迟关闭以允许点击选项
          }}
          onChange={(e) => {
            setInputValue(e.target.value);
          }}
        />
        <span className="absolute right-2 top-1/2 transform -translate-y-1/2 pointer-events-none">
          <IconTriangleDown size="15px" />
        </span>
        {showOptions && (
          <ul className="absolute z-10 w-full bg-white border border-primary rounded-md mt-1 max-h-48 overflow-y-auto">
            {filteredOptions && filteredOptions.length > 0 ? (
              filteredOptions.map((option) => (
                <li
                  key={option.value}
                  className="p-2 hover:bg-grey-200 cursor-pointer"
                  onMouseDown={() => {
                    const label =
                      i18n.language === 'en' ? option.labelEn : option.labelCh;
                    setInputValue(label);
                    onChange({
                      ...value,
                      value: value.value,
                      data: option.value,
                    });
                    setShowOptions(false);
                  }}
                >
                  {i18n.language === 'en' ? option.labelEn : option.labelCh}
                </li>
              ))
            ) : (
              <li
                className="p-2 text-center w-full text-grey-500"
                onMouseDown={(e) => e.preventDefault()}
              >
                {i18n.language === 'en'
                  ? 'No matching options'
                  : '沒有匹配的選項'}
              </li>
            )}
          </ul>
        )}
      </div>
    </div>
  );
};

export default InputSelectFilter;
