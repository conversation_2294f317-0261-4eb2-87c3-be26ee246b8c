import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  useRoute<PERSON><PERSON><PERSON>,
} from '@cdss-modules/design-system';

import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import RadixTabs from '@cdss-modules/design-system/components/_ui/RadixTabs';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';

import { useMemo, useState } from 'react';
import {
  QueryClient,
  QueryClientProvider,
  useQuery,
} from '@tanstack/react-query';
import { fireGetTabList } from '../../../../lib/content-creation/api';
import { Tab, tabComponentMap } from '../../../../types/content-creation/tab';
import React from 'react';

export function MainBody() {
  const { basePath } = useRouteHandler();

  const { t } = useTranslation();
  const [tab, setTab] = useState([]);

  const {
    data: tabList,
    isLoading: loading,
    // 重命名未使用的变量
    error: _error,
  } = useQuery({
    queryKey: ['tabList'],
    queryFn: () =>
      fireGetTabList(basePath)?.then((res) => {
        return res.data.data;
      }),
  });

  useMemo(() => {
    if (tabList) {
      // set up the tab list config
      const tabs = tabList
        .map((item: Tab) => {
          if (tabComponentMap[item.name])
            return {
              sortorder: item.sortorder,
              tabName: item.name,
              label: t(`ctint-mf-content-creation.main.tab.${item.name}`),
              content: React.createElement(tabComponentMap[item.name], {
                parentId: item.id,
              }),
            };
          return undefined;
        })
        .filter(Boolean);
      console.log('MainBody ===> tabs: ', tabs);

      setTab(tabs.sort((a: Tab, b: Tab) => a.sortorder - b.sortorder));
    }
  }, [tabList, t]);

  if (loading) {
    return (
      <div className="w-full h-full flex items-center justify-center">
        <Loader size={64} />
      </div>
    );
  }

  return (
    <AuthChecker
      // requiredPemissions={{
      //   global: {
      //     portals: ['ctint-mf-content-creation'],
      //   },
      //   user: {
      //     permissions: ['ctint-mf-content-creation.application.visit'],
      //   },
      // }}
      unAuthorizedComponent={
        <Panel>
          <h2 className="p-6 font-bold text-t6">
            You are unauthorized to use this feature.
          </h2>
        </Panel>
      }
    >
      {/* 注释掉的代码中引用了未定义的变量，已修复
      <TemplateDemo
        testId="home"
        titleI18n="ctint-mf-content-creation.templateHome.title"
        descI18n="ctint-mf-content-creation.templateHome.desc"
        btnLabelI18n="ctint-mf-content-creation.templateHome.btnLabel"
        onClickButton={() => {}}
      /> */}

      <div className="w-full h-full overflow-hidden">
        <WhitePanel className="h-full flex flex-col overflow-hidden">
          <RadixTabs
            defaultOpenTab="cannedMessage"
            tabs={tab}
          />
        </WhitePanel>
      </div>
    </AuthChecker>
  );
}

// Create a client
const queryClient = new QueryClient();

export const Main = () => (
  <QueryClientProvider client={queryClient}>
    <MainBody />
  </QueryClientProvider>
);

export default Main;
