import { Toaster, useRoute<PERSON><PERSON><PERSON> } from '@cdss-modules/design-system';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { CannedMessageTemplateBody } from '../../../_ui/content-creation/CannedMessagePanel/Category/Template';
import { WhitePanel } from '@cdss-modules/design-system/components/_ui/WhitePanel';
import { useTranslation } from 'react-i18next';
import { useSearchParams } from 'react-router-dom';
import React, { useEffect } from 'react';
import { TemplateDetail } from '../../../_ui/content-creation/CannedMessagePanel/Category/Template/templateDetail';
import type { TemplateList } from '../../../../types/content-creation/template';

const queryClient = new QueryClient();

export default function TemplateList() {
  const { t } = useTranslation();
  const [searchParams] = useSearchParams();
  const { toPath } = useRouteHandler();
  const [editMode, setEditMode] = React.useState(false);
  const [editable, setEditable] = React.useState(false);
  const [status, setStatus] = React.useState<
    undefined | 'published' | 'draft'
  >();
  const [tagStyle, setTagStyle] = React.useState('');
  const [selectedTemplate, setSelectedTemplate] = React.useState<
    TemplateList | undefined
  >();

  useEffect(() => {
    console.log('TemplateList ===> status: ', status);
    if (status === 'published') {
      setTagStyle('text-green-500 border-green-500 bg-green-100');
    } else if (status === 'draft') {
      setTagStyle('text-grey-500 border-grey-500 bg-grey-100');
    }
  }, [status]);

  useEffect(() => {
    if (!editMode) {
      // 当关闭输入模式的时候，清空选中的模板和重置可编辑状态
      setSelectedTemplate(undefined);
      setEditable(false); // 重置为不可编辑状态
    }
  }, [editMode]);

  const generateBreakcrumb = () => {
    if (editMode) {
      return (
        <>
          <span
            className="cursor-pointer text-gray-400 hover:text-black"
            onClick={() => {
              setEditMode(false);
            }}
          >
            {searchParams.get('category')}
          </span>{' '}
          /{' '}
          <span className="truncate w-[100px] inline-block align-middle">
            {selectedTemplate?.name ||
              t('ctint-mf-content-creation.template.defaultName')}
          </span>
          {status !== undefined && (
            <span
              className={`w-fit px-1 mx-1 font-light text-sm border rounded-sm ${tagStyle}`}
            >
              {status === 'published'
                ? t('ctint-mf-content-creation.template.published')
                : t('ctint-mf-content-creation.template.draft')}
            </span>
          )}
        </>
      );
    }

    return searchParams.get('category');
  };

  const generateBody = () => {
    if (editMode)
      return (
        <TemplateDetail
          editable={editable}
          queryClient={queryClient}
          setEditMode={setEditMode}
          selectedTemplate={selectedTemplate}
        />
      );
    else
      return (
        <CannedMessageTemplateBody
          queryClient={queryClient}
          setEditMode={setEditMode}
          setStatus={setStatus}
          setSelectedTemplateItem={setSelectedTemplate}
          setEditable={setEditable}
        />
      );
  };

  return (
    <QueryClientProvider client={queryClient}>
      <div className="w-full h-full overflow-hidden">
        <WhitePanel className="h-full px-4 flex flex-col overflow-hidden">
          <label className="font-normal flex-shrink-0">
            <span
              className="cursor-pointer text-gray-400 hover:text-black"
              onClick={() => {
                setEditMode(false);
                toPath('/canned');
              }}
            >
              {t('ctint-mf-content-creation.main.tab.cannedMessage')}
            </span>{' '}
            /{' '}
            <span
              className="cursor-pointer text-gray-400 hover:text-black"
              onClick={() => {
                setEditMode(false);
                toPath(
                  `/canned/category?id=${searchParams.get('spaceId')}&type=${searchParams.get('type')}&space=${searchParams.get('space')}`
                );
              }}
            >
              {searchParams.get('space')}
            </span>{' '}
            / {generateBreakcrumb()}
          </label>

          <div className="flex-1 min-h-0 overflow-hidden">{generateBody()}</div>

          <Toaster />
        </WhitePanel>
      </div>
    </QueryClientProvider>
  );
}