import { <PERSON>, Button } from '@cdss-modules/design-system';
import CDSSImage from '@cdss-modules/design-system/components/_ui/CDSSImage';
import { useTranslation } from '@cdss-modules/design-system/i18n/client';
// import { CdssUi, Button } from 'cdss-ui';

export type TemplateDemoProps = {
  testId?: string;
  titleI18n: string;
  descI18n: string;
  btnLabelI18n: string;
  onClickButton: () => void;
};
const TemplateDemo = ({
  testId,
  titleI18n,
  descI18n,
  btnLabelI18n,
  onClickButton,
}: TemplateDemoProps) => {
  const { t, i18n } = useTranslation();
  const targetLng = i18n.language === 'en' ? 'zh-HK' : 'en';
  return (
    <Panel className="p-6">
      <h1
        data-testid={`${testId}-title`}
        className="font-bold text-t4 mb-4"
      >
        {t(titleI18n)}
      </h1>
      <div className="max-w-[400px]">
        <CDSSImage
          src="/images/template.jpg"
          alt=""
        />
      </div>
      <div className="py-4">
        <p className="text-t6 mb-4">{t(descI18n)}</p>
        <Button
          data-testid={`${testId}-btn`}
          onClick={() => onClickButton()}
        >
          {t(btnLabelI18n)}
        </Button>
      </div>
      {/* <div className="py-4">
                <CdssUi />
            </div> */}
      <hr />
      <div className="py-4">
        <h1
          data-testid={`${testId}-title`}
          className="font-bold text-t5 mb-4"
        >
          {t('ctint-mf-content-creation.langDemo.languageDemo')}
        </h1>
        <p className="text-t6 mb-4">
          {t('ctint-mf-content-creation.langDemo.currentLanguage')}:{' '}
          <strong>{t('global.lng')}</strong>.{' '}
          {t('ctint-mf-content-creation.langDemo.changeLanguage')}:
        </p>
        <Button
          data-testid={`${testId}-btn`}
          onClick={() => {
            i18n.changeLanguage(targetLng);
          }}
        >{`${t('ctint-mf-content-creation.langDemo.changeTo')} ${targetLng}`}</Button>
      </div>
    </Panel>
  );
};

export default TemplateDemo;