import { Loader } from '@cdss-modules/design-system';
import IconEmptyRecords from '@cdss-modules/design-system/components/_ui/Icon/IconEmptyRecords';
import { QueryClient } from '@tanstack/react-query';
import { useSearchParams } from 'react-router-dom';
import WhatsAppDetail from './WhatsAppDetail';
import EmailDetail from './EmailDetail';
import { TemplateList } from '../../../../../types/content-creation/template';

export const TemplateDetail = ({
  queryClient,
  editable = true,
  setEditMode,
  selectedTemplate,
}: {
  queryClient: QueryClient;
  editable: boolean;
  setEditMode?: (editMode: boolean) => void;
  selectedTemplate?: TemplateList;
}) => {
  const [searchParams] = useSearchParams();
  const channelType = searchParams.get('type');

  if (channelType === 'whatsapp') {
    return (
      <WhatsAppDetail
        editable={editable}
        setEditMode={setEditMode}
        selectedTemplate={selectedTemplate}
      />
    );
  } else if (channelType === 'email') {
    return (
      <EmailDetail
        editable={editable}
        setEditMode={setEditMode}
        selectedTemplate={selectedTemplate}
      />
    );
  }

  return <IconEmptyRecords size="78" />;
};