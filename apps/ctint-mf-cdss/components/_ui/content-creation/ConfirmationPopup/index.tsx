import { X } from 'lucide-react';
import React from 'react';
import { useTranslation } from 'react-i18next';

interface ConfirmationPopupProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title?: string;
  message?: string;
  confirmText?: string;
  cancelText?: string;
}

const ConfirmationPopup: React.FC<ConfirmationPopupProps> = ({
  isOpen,
  onClose,
  onConfirm,
  title,
  message,
  confirmText,
  cancelText,
}) => {
  const { t } = useTranslation();

  if (!isOpen) return null;

  const handleConfirm = () => {
    onConfirm();
    onClose();
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div
        className="bg-white rounded-2xl shadow-2xl"
        style={{
          width: '404px',
          height: '230px',
          boxShadow: '0px 0px 32px 0px rgba(0, 0, 0, 0.16)',
        }}
      >
        {/* Title Bar */}
        <div
          className="flex justify-between items-center px-4 py-2 rounded-t-2xl"
          style={{
            backgroundColor: '#FFAC4A',
            height: '41px',
          }}
        >
          <div className="flex items-center gap-2">
            {/* Left side - empty as per design */}
          </div>
          <button
            onClick={onClose}
            className="w-5 h-5 flex items-center justify-center hover:opacity-70 transition-opacity"
          >
            <X
              size={20}
              color="#000000"
            />
          </button>
        </div>

        {/* Content */}
        <div className="flex flex-col items-center justify-center px-4 py-8">
          <div
            className="text-center text-black mb-8"
            style={{
              fontFamily: 'Roboto',
              fontWeight: 400,
              fontSize: '16px',
              lineHeight: '1.4em',
            }}
          >
            {message || t('ctint-mf-content-creation.popup.discard.message')}
          </div>

          {/* Buttons */}
          <div className="flex gap-1 px-2">
            {/* No Button */}
            <button
              onClick={onClose}
              className="border border-black bg-white text-black rounded px-2 py-1 hover:bg-gray-50 transition-colors"
              style={{
                width: '83px',
                fontFamily: 'Roboto',
                fontWeight: 400,
                fontSize: '15px',
                lineHeight: '1.4em',
              }}
            >
              {cancelText || t('ctint-mf-content-creation.popup.discard.no')}
            </button>

            {/* Yes Button */}
            <button
              onClick={handleConfirm}
              className="bg-black text-white rounded px-4 py-3 hover:bg-gray-800 transition-colors"
              style={{
                width: '83px',
                fontFamily: 'Roboto',
                fontWeight: 400,
                fontSize: '18px',
                lineHeight: '1.4em',
              }}
            >
              {confirmText || t('ctint-mf-content-creation.popup.discard.yes')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationPopup;