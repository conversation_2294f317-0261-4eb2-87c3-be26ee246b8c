import CannedMessagePanel from '../../components/_ui/content-creation/CannedMessagePanel/index';
import WhatsappTemplatePanel from '../../components/_ui/content-creation/WhatsappTemplatePanel/index';

export interface Tab {
  id: string;
  name: string;
  sortorder: number;
}

export const tabComponentMap: Record<string, React.ComponentType<any>> = {
  cannedMessage: CannedMessagePanel,
  whatsappTemplate: WhatsappTemplatePanel,
};
